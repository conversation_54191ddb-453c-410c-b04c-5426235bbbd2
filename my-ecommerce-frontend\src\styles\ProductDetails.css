/* Layout */
.product-page {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 2rem;
  gap: 2rem;
  max-width: 1200px;
  margin: auto;
}

/* Gallery */
.gallery {
  flex: 1;
}

.thumbnails {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.thumbnails img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border: 2px solid transparent;
  cursor: pointer;
  transition: border 0.2s ease-in-out;
}

.thumbnails img.active {
  border: 2px solid #007bff;
}

.main-image {
  margin-top: 1rem;
}

.main-image img {
  width: 100%;
  max-width: 500px;
  height: auto;
  object-fit: cover;
}

/* Details Section */
.details {
  flex: 1;
  max-width: 400px;
}

.details h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.options p {
  font-weight: bold;
  margin-bottom: 0.5rem;
}

/* Size Options */
.size-options {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.size-options button {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  background: none;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s, color 0.3s;
}

.size-options button.selected {
  background-color: black;
  color: white;
}

/* Color Options */
.color-options {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.color-options button {
  width: 30px;
  height: 30px;
  border: 2px solid transparent;
  cursor: pointer;
  border-radius: 50%;
}

.color-options button.selected {
  border-color: #007bff;
}

/* Price Section */
.price {
  margin: 1.5rem 0;
}

.price h2 {
  font-size: 1.5rem;
  color: #333;
}

.attribute {
  margin-bottom: 1rem;
}

.attribute-name {
  font-weight: bold;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.attribute-values {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.attribute-value-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s, color 0.3s, border-color 0.3s;
}

.attribute-value-btn:hover {
  background-color: #f0f0f0;
  border-color: black;
}

.attribute-value-btn.selected {
  background-color: black;
  color: #fff;
  border-color: #0056b3;
}

/* Add to Cart Button */
.add-to-cart {
  display: block;
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  font-weight: bold;
  color: white;
  background-color: #28a745;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
  margin: 0;
  font-family: Raleway;
}

.add-to-cart:hover {
  background-color: #218838;
}

/* Description */
.description {
  margin-top: 2rem;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #666;
}

.product-price {
  font-size: 20px;
  margin-bottom: 1rem;
}

